import type { BusinessInfo } from "@saas/onboarding/utils/google-maps";

// Website template types based on business categories
export enum WebsiteTemplate {
	RESTAURANT = "restaurant",
	RETAIL = "retail",
	SERVICES = "services",
	HEALTHCARE = "healthcare",
	FITNESS = "fitness",
	BEAUTY = "beauty",
	AUTOMOTIVE = "automotive",
	PROFESSIONAL = "professional",
	GENERIC = "generic",
}

// Website configuration interface
export interface WebsiteConfig {
	template: WebsiteTemplate;
	primaryColor: string;
	secondaryColor: string;
	fontFamily: string;
	layout: "modern" | "classic" | "minimal";
	features: WebsiteFeature[];
}

// Available website features
export enum WebsiteFeature {
	CONTACT_FORM = "contact_form",
	ONLINE_BOOKING = "online_booking",
	GALLERY = "gallery",
	MENU = "menu",
	TESTIMONIALS = "testimonials",
	LOCATION_MAP = "location_map",
	SOCIAL_LINKS = "social_links",
	BUSINESS_HOURS = "business_hours",
	SERVICES_LIST = "services_list",
	TEAM_SECTION = "team_section",
}

// Generated website data
export interface GeneratedWebsite {
	id: string;
	organizationId: string;
	businessInfo: BusinessInfo;
	config: WebsiteConfig;
	url: string;
	status: "generating" | "active" | "error";
	screenshot?: string; // URL to website screenshot
	createdAt: string;
	updatedAt: string;
	analytics: WebsiteAnalytics;
}

// Website analytics data
export interface WebsiteAnalytics {
	totalVisitors: number;
	monthlyVisitors: number;
	weeklyVisitors: number;
	dailyVisitors: number;
	pageViews: number;
	averageSessionDuration: number; // in seconds
	bounceRate: number; // percentage
	topPages: Array<{
		path: string;
		views: number;
		title: string;
	}>;
	trafficSources: Array<{
		source: string;
		visitors: number;
		percentage: number;
	}>;
	conversions: {
		contactForms: number;
		phoneClicks: number;
		directionsClicks: number;
		websiteClicks: number;
	};
}

/**
 * Determine the best website template based on business information
 */
export function determineWebsiteTemplate(
	businessInfo: BusinessInfo,
): WebsiteTemplate {
	const businessName = businessInfo.name?.toLowerCase() || "";
	const businessAddress = businessInfo.address?.toLowerCase() || "";

	// Restaurant keywords
	if (
		businessName.includes("restaurant") ||
		businessName.includes("cafe") ||
		businessName.includes("coffee") ||
		businessName.includes("bistro") ||
		businessName.includes("diner") ||
		businessName.includes("pizza") ||
		businessName.includes("bar") ||
		businessName.includes("grill")
	) {
		return WebsiteTemplate.RESTAURANT;
	}

	// Fitness keywords
	if (
		businessName.includes("gym") ||
		businessName.includes("fitness") ||
		businessName.includes("yoga") ||
		businessName.includes("pilates") ||
		businessName.includes("crossfit") ||
		businessName.includes("martial arts")
	) {
		return WebsiteTemplate.FITNESS;
	}

	// Healthcare keywords
	if (
		businessName.includes("clinic") ||
		businessName.includes("medical") ||
		businessName.includes("dental") ||
		businessName.includes("doctor") ||
		businessName.includes("hospital") ||
		businessName.includes("pharmacy")
	) {
		return WebsiteTemplate.HEALTHCARE;
	}

	// Beauty keywords
	if (
		businessName.includes("salon") ||
		businessName.includes("spa") ||
		businessName.includes("beauty") ||
		businessName.includes("barber") ||
		businessName.includes("nails") ||
		businessName.includes("massage")
	) {
		return WebsiteTemplate.BEAUTY;
	}

	// Automotive keywords
	if (
		businessName.includes("auto") ||
		businessName.includes("car") ||
		businessName.includes("garage") ||
		businessName.includes("mechanic") ||
		businessName.includes("tire") ||
		businessName.includes("repair")
	) {
		return WebsiteTemplate.AUTOMOTIVE;
	}

	// Professional services keywords
	if (
		businessName.includes("law") ||
		businessName.includes("attorney") ||
		businessName.includes("accounting") ||
		businessName.includes("consulting") ||
		businessName.includes("insurance") ||
		businessName.includes("real estate")
	) {
		return WebsiteTemplate.PROFESSIONAL;
	}

	// Retail keywords
	if (
		businessName.includes("store") ||
		businessName.includes("shop") ||
		businessName.includes("boutique") ||
		businessName.includes("market") ||
		businessName.includes("retail")
	) {
		return WebsiteTemplate.RETAIL;
	}

	// Default to services for most other businesses
	return WebsiteTemplate.SERVICES;
}

/**
 * Generate website configuration based on template and business info
 */
export function generateWebsiteConfig(
	template: WebsiteTemplate,
	_businessInfo: BusinessInfo,
): WebsiteConfig {
	const baseConfig: WebsiteConfig = {
		template,
		primaryColor: "#2563eb", // Default blue
		secondaryColor: "#64748b", // Default gray
		fontFamily: "Inter",
		layout: "modern",
		features: [
			WebsiteFeature.CONTACT_FORM,
			WebsiteFeature.LOCATION_MAP,
			WebsiteFeature.BUSINESS_HOURS,
			WebsiteFeature.SOCIAL_LINKS,
		],
	};

	// Customize based on template
	switch (template) {
		case WebsiteTemplate.RESTAURANT:
			return {
				...baseConfig,
				primaryColor: "#dc2626", // Red
				secondaryColor: "#fbbf24", // Yellow
				features: [
					...baseConfig.features,
					WebsiteFeature.MENU,
					WebsiteFeature.GALLERY,
					WebsiteFeature.ONLINE_BOOKING,
					WebsiteFeature.TESTIMONIALS,
				],
			};

		case WebsiteTemplate.FITNESS:
			return {
				...baseConfig,
				primaryColor: "#059669", // Green
				secondaryColor: "#0891b2", // Cyan
				features: [
					...baseConfig.features,
					WebsiteFeature.SERVICES_LIST,
					WebsiteFeature.ONLINE_BOOKING,
					WebsiteFeature.TEAM_SECTION,
					WebsiteFeature.GALLERY,
				],
			};

		case WebsiteTemplate.HEALTHCARE:
			return {
				...baseConfig,
				primaryColor: "#0284c7", // Blue
				secondaryColor: "#06b6d4", // Light blue
				layout: "classic",
				features: [
					...baseConfig.features,
					WebsiteFeature.SERVICES_LIST,
					WebsiteFeature.ONLINE_BOOKING,
					WebsiteFeature.TEAM_SECTION,
				],
			};

		case WebsiteTemplate.BEAUTY:
			return {
				...baseConfig,
				primaryColor: "#db2777", // Pink
				secondaryColor: "#a855f7", // Purple
				features: [
					...baseConfig.features,
					WebsiteFeature.SERVICES_LIST,
					WebsiteFeature.ONLINE_BOOKING,
					WebsiteFeature.GALLERY,
					WebsiteFeature.TESTIMONIALS,
				],
			};

		case WebsiteTemplate.RETAIL:
			return {
				...baseConfig,
				primaryColor: "#7c3aed", // Purple
				secondaryColor: "#f59e0b", // Orange
				features: [
					...baseConfig.features,
					WebsiteFeature.GALLERY,
					WebsiteFeature.TESTIMONIALS,
				],
			};

		default:
			return baseConfig;
	}
}

/**
 * Generate a website URL slug based on business name
 */
export function generateWebsiteSlug(businessName: string): string {
	return businessName
		.toLowerCase()
		.replace(/[^a-z0-9\s-]/g, "") // Remove special characters
		.replace(/\s+/g, "-") // Replace spaces with hyphens
		.replace(/-+/g, "-") // Replace multiple hyphens with single
		.trim()
		.substring(0, 50); // Limit length
}

/**
 * Generate a demo screenshot URL based on template
 */
export function generateDemoScreenshot(template: WebsiteTemplate): string {
	// In a real implementation, this would use a service like:
	// - Puppeteer/Playwright for screenshots
	// - Third-party services like ScreenshotAPI, Urlbox, etc.
	// - Vercel's screenshot service

	const screenshotMap: Record<WebsiteTemplate, string> = {
		[WebsiteTemplate.RESTAURANT]:
			"https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?w=800&h=600&fit=crop&crop=center",
		[WebsiteTemplate.RETAIL]:
			"https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800&h=600&fit=crop&crop=center",
		[WebsiteTemplate.SERVICES]:
			"https://images.unsplash.com/photo-1497366216548-37526070297c?w=800&h=600&fit=crop&crop=center",
		[WebsiteTemplate.HEALTHCARE]:
			"https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=800&h=600&fit=crop&crop=center",
		[WebsiteTemplate.FITNESS]:
			"https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop&crop=center",
		[WebsiteTemplate.BEAUTY]:
			"https://images.unsplash.com/photo-**********-b33ff0c44a43?w=800&h=600&fit=crop&crop=center",
		[WebsiteTemplate.AUTOMOTIVE]:
			"https://images.unsplash.com/photo-1503376780353-7e6692767b70?w=800&h=600&fit=crop&crop=center",
		[WebsiteTemplate.PROFESSIONAL]:
			"https://images.unsplash.com/photo-1497366754035-f200968a6e72?w=800&h=600&fit=crop&crop=center",
		[WebsiteTemplate.GENERIC]:
			"https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop&crop=center",
	};

	return screenshotMap[template] || screenshotMap[WebsiteTemplate.GENERIC];
}

/**
 * Generate demo analytics data for a new website
 */
export function generateDemoAnalytics(): WebsiteAnalytics {
	return {
		totalVisitors: Math.floor(Math.random() * 1000) + 100,
		monthlyVisitors: Math.floor(Math.random() * 500) + 50,
		weeklyVisitors: Math.floor(Math.random() * 150) + 20,
		dailyVisitors: Math.floor(Math.random() * 30) + 5,
		pageViews: Math.floor(Math.random() * 2000) + 200,
		averageSessionDuration: Math.floor(Math.random() * 180) + 60, // 1-4 minutes
		bounceRate: Math.floor(Math.random() * 40) + 30, // 30-70%
		topPages: [
			{
				path: "/",
				views: Math.floor(Math.random() * 500) + 100,
				title: "Home",
			},
			{
				path: "/about",
				views: Math.floor(Math.random() * 200) + 50,
				title: "About Us",
			},
			{
				path: "/contact",
				views: Math.floor(Math.random() * 150) + 30,
				title: "Contact",
			},
		],
		trafficSources: [
			{
				source: "Google Search",
				visitors: Math.floor(Math.random() * 300) + 100,
				percentage: 45,
			},
			{
				source: "Direct",
				visitors: Math.floor(Math.random() * 200) + 50,
				percentage: 25,
			},
			{
				source: "Social Media",
				visitors: Math.floor(Math.random() * 150) + 30,
				percentage: 20,
			},
			{
				source: "Referrals",
				visitors: Math.floor(Math.random() * 100) + 20,
				percentage: 10,
			},
		],
		conversions: {
			contactForms: Math.floor(Math.random() * 20) + 5,
			phoneClicks: Math.floor(Math.random() * 50) + 10,
			directionsClicks: Math.floor(Math.random() * 100) + 20,
			websiteClicks: Math.floor(Math.random() * 30) + 5,
		},
	};
}
