"use client";

import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import { Skeleton } from "@ui/components/skeleton";
import {
	AlertCircleIcon,
	ExternalLinkIcon,
	GlobeIcon,
	LoaderIcon,
} from "lucide-react";
import { useEffect, useState } from "react";
import {
	getTemplateInfo,
	getWebsiteByOrganization,
} from "../lib/website-service";
import type { GeneratedWebsite } from "../utils/website-generator";
import { WebsiteAnalyticsCharts } from "./WebsiteAnalyticsCharts";

export function WebsiteDashboardContent() {
	const { activeOrganization } = useActiveOrganization();
	const [website, setWebsite] = useState<GeneratedWebsite | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		if (activeOrganization) {
			loadWebsite();
		}
	}, [activeOrganization]);

	const loadWebsite = async () => {
		if (!activeOrganization) {
			return;
		}

		try {
			setLoading(true);
			const websiteData = await getWebsiteByOrganization(
				activeOrganization.id,
			);
			setWebsite(websiteData);
			setError(null);
		} catch (err) {
			setError("Failed to load website data");
			console.error("Error loading website:", err);
		} finally {
			setLoading(false);
		}
	};

	if (loading) {
		return (
			<div className="space-y-6">
				{/* Website Status Card Skeleton */}
				<Card>
					<CardHeader>
						<div className="flex items-center justify-between">
							<div className="flex-1">
								<Skeleton className="h-6 w-32 mb-2" />
								<Skeleton className="h-4 w-64" />
							</div>
							<Skeleton className="h-6 w-16" />
						</div>
					</CardHeader>
					<CardContent>
						<div className="flex flex-col sm:flex-row gap-4 sm:gap-6">
							{/* Website Screenshot Skeleton */}
							<div className="flex-shrink-0">
								<Skeleton className="w-full sm:w-48 aspect-[16/10] rounded-lg" />
							</div>

							{/* Website Details Skeleton */}
							<div className="flex-1 space-y-4">
								<div className="space-y-2">
									<div className="flex items-center gap-2">
										<Skeleton className="w-2 h-2 rounded-full" />
										<Skeleton className="h-4 w-8" />
									</div>
									<Skeleton className="h-5 w-full" />
									<Skeleton className="h-4 w-32" />
								</div>

								<div className="grid grid-cols-1 sm:grid-cols-3 gap-3 pt-4 border-t">
									<div className="text-center sm:text-left">
										<Skeleton className="h-3 w-16 mb-1" />
										<Skeleton className="h-4 w-20" />
									</div>
									<div className="text-center sm:text-left">
										<Skeleton className="h-3 w-12 mb-1" />
										<Skeleton className="h-4 w-16" />
									</div>
									<div className="text-center sm:text-left">
										<Skeleton className="h-3 w-14 mb-1" />
										<Skeleton className="h-6 w-20" />
									</div>
								</div>
							</div>
						</div>
					</CardContent>
				</Card>

				{/* Analytics Charts Skeleton */}
				<Card>
					<CardHeader>
						<Skeleton className="h-6 w-40 mb-2" />
						<Skeleton className="h-4 w-64" />
					</CardHeader>
					<CardContent>
						<div className="space-y-6">
							{/* Chart Skeleton */}
							<div className="space-y-2">
								<Skeleton className="h-4 w-24" />
								<Skeleton className="h-64 w-full rounded-lg" />
							</div>

							{/* Stats Grid Skeleton */}
							<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
								{Array.from({ length: 4 }).map((_, i) => (
									<div key={i} className="space-y-2">
										<Skeleton className="h-3 w-16" />
										<Skeleton className="h-6 w-12" />
									</div>
								))}
							</div>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	if (error) {
		return (
			<div className="flex items-center justify-center py-12">
				<div className="flex items-center gap-3 text-destructive">
					<AlertCircleIcon className="size-5" />
					<span>{error}</span>
				</div>
			</div>
		);
	}

	if (!website) {
		return (
			<div className="text-center py-12">
				<div className="max-w-md mx-auto">
					<GlobeIcon className="size-12 mx-auto text-muted-foreground mb-4" />
					<h3 className="text-lg font-semibold mb-2">
						No Website Found
					</h3>
					<p className="text-muted-foreground mb-4">
						It looks like your website hasn't been generated yet.
						This usually happens automatically during onboarding.
					</p>
					<Button onClick={loadWebsite}>Refresh</Button>
				</div>
			</div>
		);
	}

	const { config, status, url } = website;
	const templateInfo = getTemplateInfo(config.template);

	return (
		<div className="space-y-6">
			{/* Website Status Card */}
			<Card>
				<CardHeader>
					<div className="flex items-center justify-between">
						<div>
							<CardTitle className="flex items-center gap-2">
								Your Website
							</CardTitle>
							<CardDescription>
								{templateInfo.name} template •{" "}
								{templateInfo.description}
							</CardDescription>
						</div>
						<Badge
							status={
								status === "active"
									? "success"
									: status === "generating"
										? "info"
										: "error"
							}
							className="capitalize"
						>
							{status === "generating" && (
								<LoaderIcon className="size-3 mr-1 animate-spin" />
							)}
							{status}
						</Badge>
					</div>
				</CardHeader>
				<CardContent>
					<div className="flex flex-col sm:flex-row gap-4 sm:gap-6">
						{/* Website Screenshot - Smaller on mobile, compact on desktop */}
						<div className="flex-shrink-0">
							{website.screenshot ? (
								<a
									href={url}
									target="_blank"
									rel="noopener noreferrer"
									className="block relative w-full sm:w-48 aspect-[16/10] rounded-lg overflow-hidden border bg-muted group cursor-pointer transition-transform hover:scale-[1.02]"
								>
									<img
										src={website.screenshot}
										alt={`${website.businessInfo.name} website preview`}
										className="object-cover w-full h-full transition-transform group-hover:scale-105"
									/>
									<div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
									<div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors" />
									<div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
										<div className="bg-white/90 backdrop-blur-sm rounded-full p-1.5">
											<ExternalLinkIcon className="size-3 text-gray-700" />
										</div>
									</div>
								</a>
							) : (
								<div className="w-full sm:w-48 aspect-[16/10] rounded-lg border bg-muted flex items-center justify-center">
									<div className="text-center space-y-1">
										<GlobeIcon className="size-6 mx-auto text-muted-foreground" />
										<p className="text-xs text-muted-foreground">
											Generating...
										</p>
									</div>
								</div>
							)}
						</div>

						{/* Website Details - Takes remaining space */}
						<div className="flex-1 space-y-4">
							<div className="space-y-2">
								<div className="flex items-center gap-2">
									<div className="w-2 h-2 rounded-full bg-green-500" />
									<span className="text-sm font-medium">
										Live
									</span>
								</div>
								<p className="font-medium text-base sm:text-lg break-all">
									{url}
								</p>
								<p className="text-sm text-muted-foreground">
									Last updated:{" "}
									{new Date(
										website.updatedAt,
									).toLocaleDateString()}
								</p>
							</div>

							<div className="grid grid-cols-1 sm:grid-cols-3 gap-3 pt-4 border-t">
								<div className="text-center sm:text-left">
									<p className="text-xs text-muted-foreground">
										Template
									</p>
									<p className="font-medium text-sm">
										{templateInfo.name}
									</p>
								</div>
								<div className="text-center sm:text-left">
									<p className="text-xs text-muted-foreground">
										Status
									</p>
									<p className="font-medium text-sm capitalize">
										{status}
									</p>
								</div>
								<div className="text-center sm:text-left">
									<p className="text-xs text-muted-foreground">
										Actions
									</p>
									<div className="mt-1">
										<Button
											size="sm"
											variant="light"
											className="h-6 px-2 text-xs"
											asChild
										>
											<a
												href={url}
												target="_blank"
												rel="noopener noreferrer"
											>
												<ExternalLinkIcon className="size-2.5 mr-1" />
												View Site
											</a>
										</Button>
									</div>
								</div>
							</div>
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Enhanced Analytics Charts */}
			<WebsiteAnalyticsCharts website={website} />
		</div>
	);
}
