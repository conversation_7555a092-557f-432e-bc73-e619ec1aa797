"use client";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@ui/components/card";
import {
	type ChartConfig,
	ChartContainer,
	ChartTooltip,
	ChartTooltipContent,
} from "@ui/components/chart";
import {
	CalendarIcon,
	Clock,
	EyeIcon,
	Globe,
	MousePointerClickIcon,
	TrendingDownIcon,
	TrendingUpIcon,
	UsersIcon,
} from "lucide-react";
import {
	Area,
	AreaChart,
	CartesianGrid,
	ResponsiveContainer,
	XAxis,
	YAxis,
} from "recharts";
import type { GeneratedWebsite } from "../utils/website-generator";

interface WebsiteAnalyticsChartsProps {
	website: GeneratedWebsite;
}

interface KPICardProps {
	title: string;
	value: number;
	change: number;
	changeType: "positive" | "negative";
	icon: React.ComponentType<{ className?: string }>;
	format?: "number" | "percentage" | "duration";
}

// Compact KPI Component
function KPICard({
	title,
	value,
	change,
	changeType,
	icon: Icon,
	format = "number",
}: KPICardProps) {
	const formatValue = (val: number): string => {
		if (format === "percentage") {
			return `${val}%`;
		}
		if (format === "duration") {
			return `${Math.floor(val / 60)}m ${val % 60}s`;
		}
		return val.toLocaleString();
	};

	const changeColor =
		changeType === "positive" ? "text-emerald-600" : "text-red-600";
	const ChangeIcon =
		changeType === "positive" ? TrendingUpIcon : TrendingDownIcon;

	return (
		<Card className="p-4 hover:shadow-md transition-shadow">
			<div className="flex items-start justify-between">
				<div className="space-y-2">
					<div className="flex items-center space-x-2">
						<div className="p-1.5 bg-primary/10 rounded-md">
							<Icon className="h-3.5 w-3.5 text-primary" />
						</div>
						<p className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
							{title}
						</p>
					</div>
					<p className="text-2xl font-bold tracking-tight">
						{formatValue(value)}
					</p>
				</div>
				<div
					className={`flex items-center space-x-1 ${changeColor} bg-muted/50 px-2 py-1 rounded-full`}
				>
					<ChangeIcon className="h-3 w-3" />
					<span className="text-xs font-medium">
						{changeType === "positive" ? "+" : "-"}
						{Math.abs(change)}%
					</span>
				</div>
			</div>
		</Card>
	);
}

export function WebsiteAnalyticsCharts({
	website,
}: WebsiteAnalyticsChartsProps) {
	const { analytics } = website;

	// Prepare chart data (last 7 days for compact view) - memoized for performance
	const chartData = Array.from({ length: 7 }, (_, i) => {
		const date = new Date();
		date.setDate(date.getDate() - (6 - i));
		const baseVisitors = Math.floor(
			analytics.dailyVisitors || analytics.monthlyVisitors / 30 || 100,
		);
		// Use a deterministic seed based on the date for consistent data
		const seed = date.getDate() + date.getMonth() * 31;
		const variation =
			Math.floor((seed % 100) * baseVisitors * 0.003) -
			baseVisitors * 0.15;
		const visitors = Math.max(0, baseVisitors + variation);
		return {
			day: date.toLocaleDateString("en-US", { weekday: "short" }),
			visitors,
			conversions: Math.floor(visitors * 0.05),
		};
	});

	const chartConfig = {
		visitors: {
			label: "Visitors",
			color: "hsl(var(--chart-1))",
		},
		conversions: {
			label: "Actions",
			color: "hsl(var(--chart-2))",
		},
	} satisfies ChartConfig;

	return (
		<div className="space-y-6">
			{/* Header with Time Period */}
			<div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
				<div>
					<h2 className="text-xl font-semibold">Analytics</h2>
					<p className="text-sm text-muted-foreground">
						Last 30 days performance
					</p>
				</div>
				<div className="flex items-center space-x-2 text-sm text-muted-foreground bg-muted/50 px-3 py-1.5 rounded-full">
					<CalendarIcon className="h-4 w-4" />
					<span>Updated 2h ago</span>
				</div>
			</div>

			{/* KPI Grid - Mobile First */}
			<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
				<KPICard
					title="Total Visitors"
					value={analytics.totalVisitors}
					change={15}
					changeType="positive"
					icon={UsersIcon}
				/>
				<KPICard
					title="Page Views"
					value={analytics.pageViews}
					change={12}
					changeType="positive"
					icon={EyeIcon}
				/>
				<KPICard
					title="Avg. Session"
					value={analytics.averageSessionDuration}
					change={8}
					changeType="positive"
					icon={Clock}
					format="duration"
				/>
				<KPICard
					title="Bounce Rate"
					value={analytics.bounceRate}
					change={5}
					changeType="negative"
					icon={TrendingDownIcon}
					format="percentage"
				/>
			</div>

			{/* Main Analytics Grid - Mobile Responsive */}
			<div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
				{/* Left Column - Visitor Trends Split Vertically */}
				<div className="xl:col-span-2 space-y-6">
					{/* Visitor Trends Chart */}
					<Card className="h-[320px] flex flex-col">
						<CardHeader className="pb-4 flex-shrink-0">
							<div className="flex items-center justify-between">
								<div>
									<CardTitle className="text-lg">
										Visitor Trends
									</CardTitle>
									<CardDescription>
										Last 7 days activity
									</CardDescription>
								</div>
								<div className="flex items-center space-x-2 text-xs text-muted-foreground bg-muted/50 px-2 py-1 rounded-md">
									<div className="w-2 h-2 bg-chart-1 rounded-full" />
									<span>Visitors</span>
								</div>
							</div>
						</CardHeader>
						<CardContent className="flex-1 p-4">
							<ChartContainer
								config={chartConfig}
								className="w-full h-[180px]"
							>
								<ResponsiveContainer width="100%" height="100%">
									<AreaChart
										data={chartData}
										margin={{
											top: 10,
											right: 15,
											left: 10,
											bottom: 10,
										}}
									>
										<defs>
											<linearGradient
												id="visitorGradient"
												x1="0"
												y1="0"
												x2="0"
												y2="1"
											>
												<stop
													offset="0%"
													stopColor="hsl(var(--chart-1))"
													stopOpacity={0.1}
												/>
												<stop
													offset="100%"
													stopColor="hsl(var(--chart-1))"
													stopOpacity={0.02}
												/>
											</linearGradient>
										</defs>
										<CartesianGrid
											strokeDasharray="1 3"
											stroke="hsl(var(--border))"
											opacity={0.3}
											horizontal={true}
											vertical={false}
										/>
										<XAxis
											dataKey="day"
											axisLine={false}
											tickLine={false}
											tick={{
												fontSize: 11,
												fill: "hsl(var(--muted-foreground))",
											}}
											tickMargin={6}
										/>
										<YAxis
											axisLine={false}
											tickLine={false}
											tick={{
												fontSize: 11,
												fill: "hsl(var(--muted-foreground))",
											}}
											tickMargin={6}
											width={30}
										/>
										<ChartTooltip
											content={<ChartTooltipContent />}
											cursor={{
												stroke: "hsl(var(--muted-foreground))",
												strokeWidth: 1,
												opacity: 0.3,
											}}
										/>
										<Area
											type="monotone"
											dataKey="visitors"
											stroke="hsl(var(--chart-1))"
											fill="url(#visitorGradient)"
											strokeWidth={2}
											dot={false}
											activeDot={{
												r: 3,
												fill: "hsl(var(--chart-1))",
												stroke: "hsl(var(--background))",
												strokeWidth: 2,
											}}
										/>
									</AreaChart>
								</ResponsiveContainer>
							</ChartContainer>
						</CardContent>
					</Card>

					{/* Top Pages */}
					<Card className="h-[320px] flex flex-col">
						<CardHeader className="pb-4 flex-shrink-0">
							<CardTitle className="text-lg">Top Pages</CardTitle>
							<CardDescription>
								Most visited pages this month
							</CardDescription>
						</CardHeader>
						<CardContent className="flex-1 overflow-y-auto">
							<div className="space-y-3">
								{analytics.topPages
									.slice(0, 5)
									.map((page, index) => (
										<div
											key={page.path}
											className="flex items-center justify-between p-3 bg-muted/30 rounded-lg hover:bg-muted/50 transition-colors"
										>
											<div className="flex items-center space-x-3 flex-1 min-w-0">
												<div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-sm font-semibold text-primary flex-shrink-0">
													{index + 1}
												</div>
												<div className="min-w-0 flex-1">
													<div className="font-medium text-sm truncate">
														{page.title}
													</div>
													<div className="text-xs text-muted-foreground truncate">
														{page.path}
													</div>
												</div>
											</div>
											<div className="text-right flex-shrink-0 ml-4">
												<div className="font-semibold text-sm">
													{page.views.toLocaleString()}
												</div>
												<div className="text-xs text-muted-foreground">
													views
												</div>
											</div>
										</div>
									))}
							</div>
						</CardContent>
					</Card>
				</div>

				{/* Quick Stats Sidebar */}
				<div className="space-y-6">
					<Card className="h-[320px] flex flex-col">
						<CardHeader className="pb-4 flex-shrink-0">
							<CardTitle className="text-lg">
								Traffic Sources
							</CardTitle>
						</CardHeader>
						<CardContent className="flex-1 flex flex-col justify-center">
							<div className="space-y-3">
								{analytics.trafficSources
									.slice(0, 3)
									.map((source) => (
										<div
											key={source.source}
											className="flex items-center justify-between p-3 bg-muted/30 rounded-lg"
										>
											<div className="flex items-center space-x-3">
												<div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
													<Globe className="h-4 w-4 text-primary" />
												</div>
												<div>
													<p className="font-medium text-sm">
														{source.source}
													</p>
													<p className="text-xs text-muted-foreground">
														{source.percentage}%
													</p>
												</div>
											</div>
											<div className="text-right">
												<p className="font-semibold text-sm">
													{source.visitors}
												</p>
												<p className="text-xs text-muted-foreground">
													visits
												</p>
											</div>
										</div>
									))}
							</div>
						</CardContent>
					</Card>

					<Card className="h-[320px] flex flex-col">
						<CardHeader className="pb-4 flex-shrink-0">
							<CardTitle className="text-lg">
								User Actions
							</CardTitle>
						</CardHeader>
						<CardContent className="flex-1 flex flex-col justify-center">
							<div className="space-y-3">
								<div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
									<div className="flex items-center space-x-3">
										<div className="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center">
											<MousePointerClickIcon className="h-4 w-4 text-emerald-600" />
										</div>
										<span className="font-medium text-sm">
											Phone Calls
										</span>
									</div>
									<span className="font-semibold">
										{analytics.conversions.phoneClicks}
									</span>
								</div>
								<div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
									<div className="flex items-center space-x-3">
										<div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
											<Globe className="h-4 w-4 text-blue-600" />
										</div>
										<span className="font-medium text-sm">
											Directions
										</span>
									</div>
									<span className="font-semibold">
										{analytics.conversions.directionsClicks}
									</span>
								</div>
								<div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
									<div className="flex items-center space-x-3">
										<div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
											<UsersIcon className="h-4 w-4 text-purple-600" />
										</div>
										<span className="font-medium text-sm">
											Contact Forms
										</span>
									</div>
									<span className="font-semibold">
										{analytics.conversions.contactForms}
									</span>
								</div>
							</div>
						</CardContent>
					</Card>
				</div>
			</div>
		</div>
	);
}
