"use client";

import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { SettingsItem } from "@saas/shared/components/SettingsItem";
import { Button } from "@ui/components/button";
import { Label } from "@ui/components/label";
import { Switch } from "@ui/components/switch";
import { SaveIcon } from "lucide-react";
import { useEffect, useState } from "react";
import {
	getWebsiteByOrganization,
	updateWebsiteConfig,
} from "../lib/website-service";
import {
	type GeneratedWebsite,
	WebsiteFeature,
} from "../utils/website-generator";

export function WebsiteFeaturesSettings() {
	const { activeOrganization } = useActiveOrganization();
	const [website, setWebsite] = useState<GeneratedWebsite | null>(null);
	const [config, setConfig] = useState<any>(null);
	const [saving, setSaving] = useState(false);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		if (activeOrganization) {
			loadWebsite();
		}
	}, [activeOrganization]);

	const loadWebsite = async () => {
		if (!activeOrganization) {
			return;
		}

		try {
			setLoading(true);
			const websiteData = await getWebsiteByOrganization(
				activeOrganization.id,
			);
			if (websiteData) {
				setWebsite(websiteData);
				setConfig(websiteData.config);
			}
		} catch (err) {
			console.error("Error loading website:", err);
		} finally {
			setLoading(false);
		}
	};

	const handleSave = async () => {
		if (!website || !config) {
			return;
		}

		setSaving(true);
		try {
			const updatedWebsite = await updateWebsiteConfig(
				website.id,
				config,
			);
			if (updatedWebsite) {
				setWebsite(updatedWebsite);
			}
		} catch (error) {
			console.error("Failed to update website:", error);
		} finally {
			setSaving(false);
		}
	};

	const toggleFeature = (feature: WebsiteFeature) => {
		setConfig((prev: any) => ({
			...prev,
			features: prev.features.includes(feature)
				? prev.features.filter((f: WebsiteFeature) => f !== feature)
				: [...prev.features, feature],
		}));
	};

	if (loading || !website || !config) {
		return (
			<SettingsItem
				title="Website Features"
				description="Enable or disable features for your website"
			>
				<div className="space-y-6">
					<div className="space-y-4">
						{Array.from({ length: 8 }).map((_, i) => (
							<div
								key={i}
								className="flex items-center justify-between"
							>
								<div className="space-y-1">
									<Skeleton className="h-4 w-32" />
									<Skeleton className="h-3 w-24" />
								</div>
								<Skeleton className="h-6 w-11 rounded-full" />
							</div>
						))}
					</div>
					<div className="mt-4 flex justify-end">
						<Skeleton className="h-10 w-32" />
					</div>
				</div>
			</SettingsItem>
		);
	}

	const featureLabels = {
		[WebsiteFeature.CONTACT_FORM]: "Contact Form",
		[WebsiteFeature.ONLINE_BOOKING]: "Online Booking",
		[WebsiteFeature.GALLERY]: "Photo Gallery",
		[WebsiteFeature.MENU]: "Menu/Services List",
		[WebsiteFeature.TESTIMONIALS]: "Customer Testimonials",
		[WebsiteFeature.LOCATION_MAP]: "Location Map",
		[WebsiteFeature.SOCIAL_LINKS]: "Social Media Links",
		[WebsiteFeature.BUSINESS_HOURS]: "Business Hours",
		[WebsiteFeature.SERVICES_LIST]: "Services List",
		[WebsiteFeature.TEAM_SECTION]: "Team Section",
	};

	return (
		<SettingsItem
			title="Website Features"
			description="Enable or disable features for your website"
		>
			<div className="space-y-6">
				<div className="space-y-4">
					{Object.values(WebsiteFeature).map((feature) => {
						const isEnabled = config.features.includes(feature);

						return (
							<div
								key={feature}
								className="flex items-center justify-between"
							>
								<div>
									<Label
										htmlFor={feature}
										className="font-medium"
									>
										{featureLabels[feature]}
									</Label>
									<p className="text-sm text-muted-foreground">
										{feature
											.replace(/_/g, " ")
											.toLowerCase()}
									</p>
								</div>
								<Switch
									id={feature}
									checked={isEnabled}
									onCheckedChange={() =>
										toggleFeature(feature)
									}
								/>
							</div>
						);
					})}
				</div>
				<div className="mt-4 flex justify-end">
					<Button onClick={handleSave} loading={saving}>
						<SaveIcon className="size-4 mr-2" />
						Save Features
					</Button>
				</div>
			</div>
		</SettingsItem>
	);
}
