"use client";

import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { SettingsItem } from "@saas/shared/components/SettingsItem";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Input } from "@ui/components/input";
import { Label } from "@ui/components/label";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@ui/components/select";
import { Skeleton } from "@ui/components/skeleton";
import { SaveIcon } from "lucide-react";
import { useEffect, useState } from "react";
import {
	getTemplateInfo,
	getWebsiteByOrganization,
	updateWebsiteConfig,
} from "../lib/website-service";
import type { GeneratedWebsite } from "../utils/website-generator";

export function WebsiteDesignSettings() {
	const { activeOrganization } = useActiveOrganization();
	const [website, setWebsite] = useState<GeneratedWebsite | null>(null);
	const [config, setConfig] = useState<any>(null);
	const [saving, setSaving] = useState(false);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		if (activeOrganization) {
			loadWebsite();
		}
	}, [activeOrganization]);

	const loadWebsite = async () => {
		if (!activeOrganization) {
			return;
		}

		try {
			setLoading(true);
			const websiteData = await getWebsiteByOrganization(
				activeOrganization.id,
			);
			if (websiteData) {
				setWebsite(websiteData);
				setConfig(websiteData.config);
			}
		} catch (err) {
			console.error("Error loading website:", err);
		} finally {
			setLoading(false);
		}
	};

	const handleSave = async () => {
		if (!website || !config) {
			return;
		}

		setSaving(true);
		try {
			const updatedWebsite = await updateWebsiteConfig(
				website.id,
				config,
			);
			if (updatedWebsite) {
				setWebsite(updatedWebsite);
			}
		} catch (error) {
			console.error("Failed to update website:", error);
		} finally {
			setSaving(false);
		}
	};

	if (loading || !website || !config) {
		return (
			<>
				{/* Template Section Skeleton */}
				<SettingsItem
					title="Template"
					description="Your website template"
				>
					<div className="flex items-center gap-4 p-4 border rounded-lg bg-card">
						<Skeleton className="w-10 h-10 rounded-md" />
						<div className="flex-1 space-y-2">
							<Skeleton className="h-4 w-32" />
							<Skeleton className="h-3 w-48" />
						</div>
						<Skeleton className="h-5 w-12" />
					</div>
				</SettingsItem>

				{/* Colors Section Skeleton */}
				<SettingsItem
					title="Colors"
					description="Customize your website colors"
				>
					<div className="space-y-6">
						<div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
							<div className="space-y-2">
								<Skeleton className="h-4 w-24" />
								<div className="flex items-center gap-3">
									<Skeleton className="w-10 h-10 rounded-md" />
									<Skeleton className="h-10 flex-1" />
								</div>
							</div>
							<div className="space-y-2">
								<Skeleton className="h-4 w-28" />
								<div className="flex items-center gap-3">
									<Skeleton className="w-10 h-10 rounded-md" />
									<Skeleton className="h-10 flex-1" />
								</div>
							</div>
						</div>
						<div className="mt-4 flex justify-end">
							<Skeleton className="h-10 w-20" />
						</div>
					</div>
				</SettingsItem>

				{/* Layout Section Skeleton */}
				<SettingsItem
					title="Layout"
					description="Choose your website layout style"
				>
					<div className="space-y-6">
						<div className="space-y-2">
							<Skeleton className="h-4 w-20" />
							<Skeleton className="h-10 w-full" />
						</div>
						<div className="mt-4 flex justify-end">
							<Skeleton className="h-10 w-20" />
						</div>
					</div>
				</SettingsItem>
			</>
		);
	}

	const templateInfo = getTemplateInfo(config.template);

	return (
		<>
			{/* Template Section */}
			<SettingsItem title="Template" description="Your website template">
				<div className="flex items-center gap-4 p-4 border rounded-lg bg-card">
					<div
						className="w-10 h-10 rounded-md"
						style={{
							backgroundColor: templateInfo.color,
						}}
					/>
					<div className="flex-1">
						<h4 className="font-medium">{templateInfo.name}</h4>
						<p className="text-sm text-muted-foreground">
							{templateInfo.description}
						</p>
					</div>
					<Badge className="text-xs">Active</Badge>
				</div>
			</SettingsItem>

			{/* Colors Section */}
			<SettingsItem
				title="Colors"
				description="Customize your website colors"
			>
				<div className="space-y-6">
					<div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
						<div className="space-y-2">
							<Label
								htmlFor="primaryColor"
								className="text-sm font-medium"
							>
								Primary Color
							</Label>
							<div className="flex items-center gap-3">
								<Input
									id="primaryColor"
									type="color"
									value={config.primaryColor}
									onChange={(e) =>
										setConfig((prev: any) => ({
											...prev,
											primaryColor: e.target.value,
										}))
									}
									className="w-10 h-10 p-1 border rounded-md cursor-pointer"
								/>
								<Input
									value={config.primaryColor}
									onChange={(e) =>
										setConfig((prev: any) => ({
											...prev,
											primaryColor: e.target.value,
										}))
									}
									placeholder="#2563eb"
									className="flex-1 font-mono text-sm"
								/>
							</div>
						</div>
						<div className="space-y-2">
							<Label
								htmlFor="secondaryColor"
								className="text-sm font-medium"
							>
								Secondary Color
							</Label>
							<div className="flex items-center gap-3">
								<Input
									id="secondaryColor"
									type="color"
									value={config.secondaryColor}
									onChange={(e) =>
										setConfig((prev: any) => ({
											...prev,
											secondaryColor: e.target.value,
										}))
									}
									className="w-10 h-10 p-1 border rounded-md cursor-pointer"
								/>
								<Input
									value={config.secondaryColor}
									onChange={(e) =>
										setConfig((prev: any) => ({
											...prev,
											secondaryColor: e.target.value,
										}))
									}
									placeholder="#64748b"
									className="flex-1 font-mono text-sm"
								/>
							</div>
						</div>
					</div>
					<div className="mt-4 flex justify-end">
						<Button
							onClick={handleSave}
							disabled={saving}
							loading={saving}
						>
							<SaveIcon className="size-4 mr-2" />
							Save
						</Button>
					</div>
				</div>
			</SettingsItem>

			{/* Layout Section */}
			<SettingsItem
				title="Layout"
				description="Choose your website layout style"
			>
				<div className="space-y-6">
					<div className="space-y-2">
						<Label className="text-sm font-medium">
							Layout Type
						</Label>
						<Select
							value={config.layout}
							onValueChange={(
								value: "modern" | "classic" | "minimal",
							) =>
								setConfig((prev: any) => ({
									...prev,
									layout: value,
								}))
							}
						>
							<SelectTrigger className="w-full">
								<SelectValue placeholder="Select a layout style" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="modern">Modern</SelectItem>
								<SelectItem value="classic">Classic</SelectItem>
								<SelectItem value="minimal">Minimal</SelectItem>
							</SelectContent>
						</Select>
					</div>
					<div className="mt-4 flex justify-end">
						<Button
							onClick={handleSave}
							disabled={saving}
							loading={saving}
						>
							<SaveIcon className="size-4 mr-2" />
							Save
						</Button>
					</div>
				</div>
			</SettingsItem>
		</>
	);
}
