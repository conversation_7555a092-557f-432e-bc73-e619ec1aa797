import type { BusinessInfo } from "@saas/onboarding/utils/google-maps";
import {
	type GeneratedWebsite,
	WebsiteTemplate,
	determineWebsiteTemplate,
	generateDemoAnalytics,
	generateDemoScreenshot,
	generateWebsiteConfig,
	generateWebsiteSlug,
} from "../utils/website-generator";

/**
 * Generate a website from business information
 */
export async function generateWebsite(
	organizationId: string,
	businessInfo: BusinessInfo,
): Promise<GeneratedWebsite> {
	// Determine the best template for this business
	const template = determineWebsiteTemplate(businessInfo);

	// Generate website configuration
	const config = generateWebsiteConfig(template, businessInfo);

	// Generate website URL slug
	const slug = generateWebsiteSlug(businessInfo.name || "business");

	// Create website data
	const website: GeneratedWebsite = {
		id: `website_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
		organizationId,
		businessInfo,
		config,
		url: `https://${slug}.pintosite.com`, // Using Pintosite subdomain
		status: "generating",
		screenshot: generateDemoScreenshot(template),
		createdAt: new Date().toISOString(),
		updatedAt: new Date().toISOString(),
		analytics: generateDemoAnalytics(),
	};

	// Simulate website generation process
	setTimeout(async () => {
		await completeWebsiteGeneration(website.id);
	}, 3000); // 3 second delay to simulate generation

	// Store website data (in a real app, this would go to database)
	await storeWebsite(website);

	return website;
}

/**
 * Complete website generation (simulate deployment)
 */
async function completeWebsiteGeneration(websiteId: string): Promise<void> {
	// In a real implementation, this would:
	// 1. Generate HTML/CSS/JS files based on template and business data
	// 2. Deploy to hosting service (Vercel, Netlify, etc.)
	// 3. Set up custom domain/subdomain
	// 4. Configure analytics tracking
	// 5. Update website status to 'active'

	console.log(`Website ${websiteId} generation completed`);

	// Update status to active
	const websites = getStoredWebsites();
	const website = websites.find((w) => w.id === websiteId);
	if (website) {
		website.status = "active";
		website.updatedAt = new Date().toISOString();
		updateStoredWebsites(websites);
	}
}

/**
 * Get website by organization ID
 */
export async function getWebsiteByOrganization(
	organizationId: string,
): Promise<GeneratedWebsite | null> {
	const websites = getStoredWebsites();
	const website = websites.find((w) => w.organizationId === organizationId);

	// If website exists but doesn't have a screenshot, add one
	if (website && !website.screenshot) {
		website.screenshot = generateDemoScreenshot(website.config.template);
		updateStoredWebsites(websites);
	}

	return website || null;
}

/**
 * Update website configuration
 */
export async function updateWebsiteConfig(
	websiteId: string,
	configUpdates: Partial<GeneratedWebsite["config"]>,
): Promise<GeneratedWebsite | null> {
	const websites = getStoredWebsites();
	const website = websites.find((w) => w.id === websiteId);

	if (!website) {
		return null;
	}

	website.config = { ...website.config, ...configUpdates };
	website.updatedAt = new Date().toISOString();

	updateStoredWebsites(websites);

	// In a real implementation, this would trigger website rebuild
	console.log(`Website ${websiteId} configuration updated`);

	return website;
}

/**
 * Get website analytics
 */
export async function getWebsiteAnalytics(
	websiteId: string,
): Promise<GeneratedWebsite["analytics"] | null> {
	const websites = getStoredWebsites();
	const website = websites.find((w) => w.id === websiteId);

	if (!website) {
		return null;
	}

	// In a real implementation, this would fetch real analytics data
	// For demo, we'll update with fresh random data
	website.analytics = generateDemoAnalytics();
	updateStoredWebsites(websites);

	return website.analytics;
}

/**
 * Delete website
 */
export async function deleteWebsite(websiteId: string): Promise<boolean> {
	const websites = getStoredWebsites();
	const index = websites.findIndex((w) => w.id === websiteId);

	if (index === -1) {
		return false;
	}

	websites.splice(index, 1);
	updateStoredWebsites(websites);

	// In a real implementation, this would:
	// 1. Remove website files from hosting
	// 2. Delete custom domain configuration
	// 3. Clean up analytics data

	console.log(`Website ${websiteId} deleted`);

	return true;
}

/**
 * Store website data (demo implementation using localStorage)
 * In production, this would use a database
 */
async function storeWebsite(website: GeneratedWebsite): Promise<void> {
	const websites = getStoredWebsites();

	// Remove any existing website for this organization
	const filteredWebsites = websites.filter(
		(w) => w.organizationId !== website.organizationId,
	);

	// Add new website
	filteredWebsites.push(website);

	updateStoredWebsites(filteredWebsites);
}

/**
 * Get stored websites from localStorage (demo implementation)
 */
function getStoredWebsites(): GeneratedWebsite[] {
	if (typeof window === "undefined") {
		return [];
	}

	try {
		const stored = localStorage.getItem("pintosite_websites");
		return stored ? JSON.parse(stored) : [];
	} catch {
		return [];
	}
}

/**
 * Update stored websites in localStorage (demo implementation)
 */
function updateStoredWebsites(websites: GeneratedWebsite[]): void {
	if (typeof window === "undefined") {
		return;
	}

	try {
		localStorage.setItem("pintosite_websites", JSON.stringify(websites));
	} catch (error) {
		console.error("Failed to store websites:", error);
	}
}

/**
 * Get website template information
 */
export function getTemplateInfo(template: WebsiteTemplate): {
	name: string;
	description: string;
	features: string[];
	color: string;
} {
	const templateInfo = {
		[WebsiteTemplate.RESTAURANT]: {
			name: "Restaurant",
			description: "Perfect for restaurants, cafes, and food businesses",
			features: [
				"Menu Display",
				"Online Reservations",
				"Photo Gallery",
				"Customer Reviews",
			],
			color: "#dc2626",
		},
		[WebsiteTemplate.FITNESS]: {
			name: "Fitness",
			description:
				"Ideal for gyms, fitness centers, and personal trainers",
			features: [
				"Class Schedules",
				"Trainer Profiles",
				"Membership Info",
				"Online Booking",
			],
			color: "#059669",
		},
		[WebsiteTemplate.HEALTHCARE]: {
			name: "Healthcare",
			description:
				"Designed for medical practices and healthcare providers",
			features: [
				"Appointment Booking",
				"Service Information",
				"Doctor Profiles",
				"Patient Resources",
			],
			color: "#0284c7",
		},
		[WebsiteTemplate.BEAUTY]: {
			name: "Beauty & Wellness",
			description: "Great for salons, spas, and beauty services",
			features: [
				"Service Menu",
				"Online Booking",
				"Before/After Gallery",
				"Staff Profiles",
			],
			color: "#db2777",
		},
		[WebsiteTemplate.RETAIL]: {
			name: "Retail",
			description: "Perfect for shops, boutiques, and retail businesses",
			features: [
				"Product Showcase",
				"Store Information",
				"Customer Testimonials",
				"Contact Form",
			],
			color: "#7c3aed",
		},
		[WebsiteTemplate.AUTOMOTIVE]: {
			name: "Automotive",
			description:
				"Designed for auto repair, dealerships, and car services",
			features: [
				"Service List",
				"Appointment Booking",
				"Customer Reviews",
				"Location Map",
			],
			color: "#ea580c",
		},
		[WebsiteTemplate.PROFESSIONAL]: {
			name: "Professional Services",
			description:
				"Ideal for lawyers, consultants, and professional services",
			features: [
				"Service Areas",
				"Team Profiles",
				"Case Studies",
				"Contact Forms",
			],
			color: "#1f2937",
		},
		[WebsiteTemplate.SERVICES]: {
			name: "General Services",
			description: "Versatile template for service-based businesses",
			features: [
				"Service Descriptions",
				"Contact Information",
				"Business Hours",
				"Location Map",
			],
			color: "#2563eb",
		},
		[WebsiteTemplate.GENERIC]: {
			name: "Generic Business",
			description: "Clean template suitable for any type of business",
			features: [
				"About Section",
				"Contact Form",
				"Business Information",
				"Location Map",
			],
			color: "#6b7280",
		},
	};

	return templateInfo[template];
}
