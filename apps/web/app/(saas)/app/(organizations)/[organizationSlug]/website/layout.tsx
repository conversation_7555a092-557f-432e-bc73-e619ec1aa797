import { getActiveOrganization } from "@saas/auth/lib/server";
import { OrganizationLogo } from "@saas/organizations/components/OrganizationLogo";
import { PageHeader } from "@saas/shared/components/PageHeader";
import { SidebarContentLayout } from "@saas/shared/components/SidebarContentLayout";
import { WebsiteSettingsMenu } from "@saas/websites/components/WebsiteSettingsMenu";
import {
	BarChart3Icon,
	GlobeIcon,
	PaletteIcon,
	SettingsIcon,
} from "lucide-react";
import { redirect } from "next/navigation";
import type { PropsWithChildren } from "react";

export default async function WebsiteLayout({
	children,
	params,
}: PropsWithChildren<{
	params: Promise<{ organizationSlug: string }>;
}>) {
	const { organizationSlug } = await params;
	const organization = await getActiveOrganization(organizationSlug);

	if (!organization) {
		redirect("/app");
	}

	const websiteBasePath = `/app/${organizationSlug}/website`;

	const menuItems = [
		{
			title: "Website Management",
			avatar: (
				<OrganizationLogo
					name={organization.name}
					logoUrl={organization.logo}
				/>
			),
			items: [
				{
					title: "Dashboard",
					href: `${websiteBasePath}`,
					icon: <BarChart3Icon className="size-4 opacity-50" />,
				},
				{
					title: "Design",
					href: `${websiteBasePath}/design`,
					icon: <PaletteIcon className="size-4 opacity-50" />,
				},
				{
					title: "Content",
					href: `${websiteBasePath}/content`,
					icon: <GlobeIcon className="size-4 opacity-50" />,
				},
				{
					title: "Features",
					href: `${websiteBasePath}/features`,
					icon: <SettingsIcon className="size-4 opacity-50" />,
				},
			],
		},
	];

	return (
		<>
			<PageHeader
				title={organization.name}
				subtitle="Manage your business website and view analytics"
			/>
			<SidebarContentLayout
				sidebar={<WebsiteSettingsMenu menuItems={menuItems} />}
			>
				{children}
			</SidebarContentLayout>
		</>
	);
}
